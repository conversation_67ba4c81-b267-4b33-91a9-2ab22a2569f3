{"permissions": {"allow": ["Bash(pnpm:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(chmod:*)", "Bash(find:*)", "<PERSON><PERSON>(uv:*)", "Bash(gh:*)", "Bash(ls:*)", "Edit(*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(uv run:*)", "Bash(pnpm format:*)", "Bash(pnpm lint:fix:*)", "<PERSON><PERSON>(pre-commit run:*)", "Bash(pnpm test:backend:*)"], "deny": []}, "includeCoAuthoredBy": false}