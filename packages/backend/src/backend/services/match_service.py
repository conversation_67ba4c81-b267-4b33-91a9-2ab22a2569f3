"""Match management service."""

from dataclasses import dataclass

from backend import crud_async, schemas
from backend.clients.steam_api import MatchInfo, SteamAPI
from backend.database import get_async_connection
from backend.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class MatchService:
    """Service for managing matches and match data."""

    steam_api: SteamAPI

    async def get_match_info(self, match_id: str) -> MatchInfo:
        """Get information about a match from Steam.

        Args:
            match_id: The match ID to get information for

        Returns:
            Match information from Steam API
        """
        return await self.steam_api.get_match_info(match_id)

    @staticmethod
    async def list_matches() -> list[schemas.MatchRead]:
        """List all matches.

        Returns:
            List of match records with players and rounds
        """
        async with get_async_connection() as db:
            return await crud_async.list_matches(db)

    @staticmethod
    async def get_match(match_id: int) -> schemas.MatchRead:
        """Get a match by ID.

        Args:
            match_id: The ID of the match to retrieve

        Returns:
            Match record with players and rounds
        """
        async with get_async_connection() as db:
            return await crud_async.read_match(match_id, db)

    @staticmethod
    async def get_match_by_demo(demo_id: int) -> schemas.MatchRead:
        """Get match data for a demo file.

        Args:
            demo_id: The ID of the demo file

        Returns:
            Match record with players and rounds
        """
        async with get_async_connection() as db:
            return await crud_async.read_match_by_demo_file(demo_id, db)
