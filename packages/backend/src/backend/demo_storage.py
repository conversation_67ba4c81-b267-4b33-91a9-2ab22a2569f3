import logging
import re
from datetime import UTC, datetime, timedelta
from pathlib import Path

from sqlalchemy import Connection, text

from backend.config import settings
from backend.schemas import DemoFileStatus


class DemoStorageError(Exception):
    """Base exception for demo storage related errors."""


class DemoStorage:
    """Handles demo file storage operations."""

    storage_path: Path

    def __init__(self):
        """Initialize the DemoStorage with the configured storage path."""
        self.storage_path = Path(settings.demo_storage_path)
        if not self.storage_path.exists():
            self.storage_path.mkdir(parents=True)

    def generate_file_path(self, match_id: str) -> Path:
        """Generate a unique file path for a demo file.

        Args:
            match_id: The match ID to generate a path for.

        Returns:
            Path object for the demo file.
        """
        timestamp = datetime.now(UTC).strftime("%Y%m%d_%H%M%S")
        filename = f"{match_id}_{timestamp}.dem"
        return self.storage_path / filename

    @staticmethod
    def extract_match_id(filename: str) -> str:
        """Extract match_id from a demo filename.

        The filename format is: match_id_YYYYMMDD_HHMMSS.dem
        We need to extract everything before the timestamp part.

        Args:
            filename: Name of the demo file

        Returns:
            The extracted match_id
        """
        # Match timestamp pattern at the end of the filename
        timestamp_pattern = r"_\d{8}_\d{6}\.dem$"
        match = re.search(timestamp_pattern, filename)
        if match:
            return filename[: match.start()]  # Everything before the timestamp

        # Fallback: return everything before the extension
        return Path(filename).stem

    def cleanup_old_demos(self, conn: Connection) -> list[Path]:
        """Remove demo files older than DEMO_RETENTION_DAYS that have been processed.

        Args:
            conn: Database connection for querying demo status.

        Returns:
            List of removed file paths.
        """
        logger = logging.getLogger(__name__)
        cutoff_date = datetime.now(UTC) - timedelta(days=settings.demo_retention_days)
        removed_files: list[Path] = []

        # Find all demo files in storage
        for demo_path in self.storage_path.glob("*.dem"):
            try:
                # Check file modification time
                mod_time = demo_path.stat().st_mtime
                if mod_time < cutoff_date.timestamp():
                    # Get the match_id from the filename
                    match_id = DemoStorage.extract_match_id(demo_path.name)

                    logger.info(
                        "Checking demo file: %s with match_id: %s",
                        demo_path.name,
                        match_id,
                    )

                    # Query for processed demo file using raw SQL
                    result = conn.execute(
                        text("""
                            SELECT id, match_id FROM demo_file
                            WHERE match_id = :match_id AND status = :status
                        """),
                        {"match_id": match_id, "status": DemoFileStatus.PROCESSED},
                    ).first()

                    if result:
                        logger.info(
                            "Found matching demo file in DB: %s", result.match_id
                        )
                        demo_path.unlink()
                        removed_files.append(demo_path)
                        logger.info("Removed file: %s", demo_path)
                    else:
                        logger.info(
                            "No matching processed demo file found for %s", match_id
                        )
            except OSError:
                # Log error but continue with other files
                logger.exception("Error cleaning up demo %s", demo_path)

        return removed_files

    async def save_demo_file(self, match_id: str, data: bytes) -> Path:
        """Save a demo file to storage.

        Args:
            match_id: The match ID for the demo.
            data: The demo file data to save.

        Returns:
            Path where the file was saved.

        Raises:
            DemoStorageError: If there's an error saving the file.
        """
        file_path = self.generate_file_path(match_id)

        try:
            _ = file_path.write_bytes(data)
        except Exception as e:
            error_msg = f"Failed to save demo file: {e!s}"
            raise DemoStorageError(error_msg) from e

        return file_path

    @staticmethod
    def delete_demo_file(file_path: str) -> None:
        """Delete a demo file from storage.

        Args:
            file_path: Path to the demo file to delete.

        Raises:
            DemoStorageError: If the file doesn't exist or can't be deleted.
        """
        path = Path(file_path)
        if not path.exists():
            error_msg = f"Demo file not found: {file_path}"
            raise DemoStorageError(error_msg)

        try:
            path.unlink()
        except OSError as e:
            error_msg = f"Failed to delete demo file: {e!s}"
            raise DemoStorageError(error_msg) from e


def get_demo_storage() -> DemoStorage:
    """Dependency for getting a DemoStorage instance."""
    return DemoStorage()
