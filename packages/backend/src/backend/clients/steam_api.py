import json
from datetime import UTC, datetime
from typing import Any, Final

import httpx
from fastapi import HTTPException, status
from pydantic import BaseModel, TypeAdapter

from backend.config import settings


# Pydantic models for structured data
class SteamPlayer(BaseModel):
    """Data about a player from the Steam API."""

    steamid: str
    personaname: str
    avatarfull: str
    communityvisibilitystate: int | None = None
    profilestate: int | None = None
    lastlogoff: int | None = None
    commentpermission: int | None = None


class SteamPlayerSummary(BaseModel):
    """Data about a player from the Steam API."""

    steamid: str
    personaname: str
    avatarfull: str


class PlayerData(BaseModel):
    """Data about a player from the Steam API."""

    steamid: str
    personaname: str
    avatarfull: str


class PlayerSummaryResponse(BaseModel):
    """Response from the Steam API for player summaries."""

    response: dict[str, list[SteamPlayer]]


class MatchHistoryParams(BaseModel):
    """Parameters for the Steam API match history request."""

    steamid: str
    steamidkey: str
    knowncode: str


class NextMatchCodeResponse(BaseModel):
    """Response from the Steam API for next match code."""

    result: dict[str, str]


class MatchInfo(BaseModel):
    """Information about a CS:GO match."""

    match_id: str
    demo_url: str
    timestamp: int | None = None
    map_name: str | None = None
    score: str | None = None


class SteamApiError(Exception):
    """Base exception for Steam API related errors."""

    status_code: int

    def __init__(self, message: str, status_code: int):
        """Initialize the SteamApiError.

        Args:
            message: The error message
            status_code: The HTTP status code
        """
        super().__init__(message)
        self.status_code = status_code


class RateLimitError(SteamApiError):
    """Raised when the Steam API rate limit is exceeded."""

    def __init__(self, message: str = "Steam API rate limit exceeded"):
        """Initialize the RateLimitError.

        Args:
            message: The error message
        """
        super().__init__(message, status.HTTP_429_TOO_MANY_REQUESTS)


class AuthenticationError(SteamApiError):
    """Raised when authentication with Steam API fails."""

    def __init__(self, message: str = "Steam API authentication failed"):
        """Initialize the AuthenticationError.

        Args:
            message: The error message
        """
        super().__init__(message, status.HTTP_403_FORBIDDEN)


class InvalidCodeError(SteamApiError):
    """Raised when an invalid match code is provided."""

    def __init__(self, message: str = "Invalid match code"):
        """Initialize the InvalidCodeError.

        Args:
            message: The error message
        """
        super().__init__(message, status.HTTP_412_PRECONDITION_FAILED)


class SteamAPI:
    """Client for interacting with the Steam API."""

    # Define API URLs as class constants
    PLAYER_SUMMARY_URL: Final[str] = (
        "https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/"
    )
    MATCH_HISTORY_URL: Final[str] = (
        "https://api.steampowered.com/ICSGOPlayers_730/GetNextMatchSharingCode/v1"
    )

    def __init__(self):
        """Initialize the SteamAPI client.

        Raises:
            ValueError: If the Steam API key is not configured
        """
        error_msg = "Steam API key not configured"
        if not settings.steam_api_key:
            raise ValueError(error_msg)
        self.api_key: str = settings.steam_api_key
        self.timeout: int = settings.request_timeout

        # TypeAdapters for response validation
        self.player_summary_adapter: TypeAdapter[PlayerSummaryResponse] = TypeAdapter(
            PlayerSummaryResponse
        )
        self.next_match_adapter: TypeAdapter[NextMatchCodeResponse] = TypeAdapter(
            NextMatchCodeResponse
        )

    @staticmethod
    def _handle_response_errors(response: httpx.Response) -> None:
        """Handle common HTTP response errors from Steam API.

        Args:
            response: The HTTP response to check

        Raises:
            RateLimitError: If rate limit exceeded
            AuthenticationError: If authentication failed
            InvalidCodeError: If precondition failed
        """
        if response.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
            raise RateLimitError
        if response.status_code == status.HTTP_403_FORBIDDEN:
            raise AuthenticationError
        if response.status_code == status.HTTP_412_PRECONDITION_FAILED:
            raise InvalidCodeError

    async def _make_request(
        self, url: str, params: dict[str, str]
    ) -> PlayerSummaryResponse | NextMatchCodeResponse:
        """Make a request to the Steam API with error handling.

        Args:
            url: The API endpoint URL
            params: Request parameters

        Returns:
            Validated response data as either PlayerSummaryResponse or NextMatchCodeResponse

        Raises:
            HTTPException: For various API errors
        """
        # Add API key to all requests
        params["key"] = self.api_key

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params)

                # Handle common status codes
                self._handle_response_errors(response)

                _ = response.raise_for_status()
                raw_data: Any = response.json()  # pyright: ignore[reportExplicitAny, reportAny]

                # Validate response based on URL
                if url == self.PLAYER_SUMMARY_URL:
                    return self.player_summary_adapter.validate_python(raw_data)
                return self.next_match_adapter.validate_python(raw_data)

        except httpx.TimeoutException as e:
            raise HTTPException(
                status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                detail="Steam API request timed out",
            ) from e
        except (RateLimitError, AuthenticationError, InvalidCodeError) as e:
            raise HTTPException(status_code=e.status_code, detail=str(e)) from e
        except httpx.HTTPStatusError as e:
            status_code = e.response.status_code
            detail = f"Steam API error: {status_code}"
            raise HTTPException(status_code=status_code, detail=detail) from e
        except httpx.RequestError as e:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"Error communicating with Steam API: {e!s}",
            ) from e
        except json.JSONDecodeError as e:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="Invalid response format from Steam API",
            ) from e

    async def get_player_summary(self, steam_id: str) -> SteamPlayerSummary:
        """Fetch a player's summary from the Steam API.

        Args:
            steam_id: The Steam ID of the player

        Returns:
            SteamPlayerSummary containing the player's information

        Raises:
            HTTPException: If there's an error fetching the data
        """
        params: dict[str, str] = {"steamids": steam_id}
        try:
            response = await self._make_request(self.PLAYER_SUMMARY_URL, params)
            if not isinstance(response, PlayerSummaryResponse):
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail="Invalid response type from Steam API",
                )

            players = response.response.get("players", [])
            if not players:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Steam user not found",
                )

            player_data = players[0]
            # Use model_dump() instead of deprecated dict()
            return SteamPlayerSummary(
                steamid=player_data.steamid,
                personaname=player_data.personaname,
                avatarfull=player_data.avatarfull,
            )
        except KeyError as e:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"Unexpected response format from Steam API: {e!s}",
            ) from e

    async def get_next_match_code(
        self, steamid: str, steamidkey: str, knowncode: str
    ) -> str:
        """Fetch the next match code from the Steam API match history.

        Args:
            steamid: The Steam ID of the player
            steamidkey: The game authentication code
            knowncode: A known match sharing code

        Returns:
            str: The next match code, or "n/a" if no more matches

        Raises:
            HTTPException: For various API errors
        """
        params: dict[str, str] = {
            "steamid": steamid,
            "steamidkey": steamidkey,
            "knowncode": knowncode,
        }

        try:
            response = await self._make_request(self.MATCH_HISTORY_URL, params)
            if not isinstance(response, NextMatchCodeResponse):
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail="Invalid response type from Steam API",
                )

            if "nextcode" not in response.result:
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail="Unexpected response format from Steam API",
                )

            return response.result["nextcode"]
        except KeyError as e:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"Unexpected response format from Steam API: {e!s}",
            ) from e

    @staticmethod
    async def get_match_info(match_id: str) -> MatchInfo:
        """Get information about a match from Steam.

        Args:
            match_id: The match ID to get information for

        Returns:
            MatchInfo: Information about the match
        """
        # This is a placeholder implementation
        # In a real implementation, you would make an API call to Steam
        # to get the match information

        # For now, we'll just return a dummy response with the match ID
        # and a demo URL
        demo_url = (
            f"https://replay{match_id}.valve.net/730/{match_id}_{match_id}.dem.bz2"
        )

        return MatchInfo(
            match_id=match_id,
            demo_url=demo_url,
            timestamp=int(datetime.now(UTC).timestamp()),
            map_name="de_dust2",  # Placeholder
            score="16-14",  # Placeholder
        )


def get_steam_api() -> SteamAPI:
    """Dependency for getting a SteamAPI instance."""
    return SteamAPI()
