"""Async CRUD operations using python-databases package."""

from datetime import UTC, datetime
from typing import Annotated, Any

import databases
from fastapi import Depends, HTTPException, status

from backend.clients.steam_api import SteamAPI, get_steam_api
from backend.database import get_async_connection_dependency
from backend.schemas import (
    DemoFileCreate,
    DemoFileRead,
    MatchPlayerRead,
    MatchRead,
    MatchRoundRead,
    TrackingDetailsCreate,
    TrackingDetailsRead,
    UserCreate,
    UserRead,
    UserReadWithTracking,
)


def _raise_tracking_creation_error() -> None:
    """Raise an HTTP exception for tracking details creation failure."""
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Failed to create tracking details",
    )


def _raise_demo_creation_error() -> None:
    """Raise an HTTP exception for demo file creation failure."""
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Failed to create demo file",
    )


def _raise_demo_not_found_error() -> None:
    """Raise an HTTP exception for demo file not found."""
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND, detail="Demo file not found"
    )


def _raise_match_creation_error() -> None:
    """Raise an HTTP exception for match creation failure."""
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Failed to create match",
    )


async def create_user(
    user: UserCreate,
    db: Annotated[databases.Database, Depends(get_async_connection_dependency)],
    steam_api: Annotated[SteamAPI, Depends(get_steam_api)],
) -> UserRead | None:
    """Create a new user.

    Args:
        user: User data to create
        db: Database connection
        steam_api: Steam API client

    Returns:
        Created user record

    Raises:
        HTTPException: If user creation fails
    """
    existing_user = await read_user_by_steam_id(
        user.steam_id, db, raise_if_not_found=False
    )
    if existing_user:
        return None

    steam_profile = await steam_api.get_player_summary(user.steam_id)

    # Insert new user
    query = """
        INSERT INTO "user" (steam_id, username, profile_picture_url, created_at)
        VALUES (:steam_id, :username, :profile_picture_url, :created_at)
        RETURNING id, steam_id, username, profile_picture_url, created_at
    """

    values = {
        "steam_id": user.steam_id,
        "username": str(steam_profile.personaname),
        "profile_picture_url": steam_profile.avatarfull,
        "created_at": datetime.now(UTC),
    }

    try:
        user_row = await db.fetch_one(query, values)
        if user_row:
            # Use Pydantic to validate and type the database result
            return UserRead(
                id=user_row["id"],
                steam_id=user_row["steam_id"],
                username=user_row["username"],
                profile_picture_url=user_row["profile_picture_url"],
                created_at=user_row["created_at"],
            )
    except Exception as e:
        # Handle potential database errors (e.g., unique constraint violations)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create user: {e!s}",
        ) from e

    return None


async def read_user_by_steam_id(
    steam_id: str,
    db: Annotated[databases.Database, Depends(get_async_connection_dependency)],
    *,
    raise_if_not_found: bool = True,
) -> UserRead | None:
    """Read a user by their Steam ID.

    Args:
        steam_id: Steam ID of the user
        db: Database connection
        raise_if_not_found: Whether to raise an HTTPException if the user is not found

    Returns:
        User record

    Raises:
        HTTPException: If user is not found and raise_if_not_found is True
    """
    query = """
        SELECT id, steam_id, username, profile_picture_url, created_at
        FROM "user"
        WHERE steam_id = :steam_id
    """

    user_row = await db.fetch_one(query, {"steam_id": steam_id})

    if user_row is None and raise_if_not_found:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    if user_row:
        # Use Pydantic to validate and type the database result
        return UserRead(
            id=user_row["id"],
            steam_id=user_row["steam_id"],
            username=user_row["username"],
            profile_picture_url=user_row["profile_picture_url"],
            created_at=user_row["created_at"],
        )
    return None


async def read_user(
    user_id: int,
    db: Annotated[databases.Database, Depends(get_async_connection_dependency)],
) -> UserReadWithTracking:
    """Read a user by their ID.

    Args:
        user_id: ID of the user
        db: Database connection

    Returns:
        User record with tracking information

    Raises:
        HTTPException: If user is not found
    """
    query = """
        SELECT u.id, u.steam_id, u.username, u.profile_picture_url, u.created_at,
               CASE WHEN t.id IS NOT NULL THEN 1 ELSE 0 END as has_tracking
        FROM "user" u
        LEFT JOIN tracking_details t ON u.id = t.user_id
        WHERE u.id = :user_id
    """

    user_row = await db.fetch_one(query, {"user_id": user_id})

    if user_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Use Pydantic to validate and type the database result
    return UserReadWithTracking(
        id=user_row["id"],
        steam_id=user_row["steam_id"],
        username=user_row["username"],
        profile_picture_url=user_row["profile_picture_url"],
        created_at=user_row["created_at"],
        has_tracking=bool(user_row["has_tracking"]),
    )


async def is_user_registered(
    steam_id: str,
    db: Annotated[databases.Database, Depends(get_async_connection_dependency)],
) -> bool:
    """Check if a user is registered.

    Args:
        steam_id: Steam ID of the user
        db: Database connection

    Returns:
        True if user is registered, False otherwise
    """
    user = await read_user_by_steam_id(steam_id, db, raise_if_not_found=False)
    return user is not None


async def create_tracking_details(
    user_id: int,
    tracking: TrackingDetailsCreate,
    db: databases.Database,
) -> TrackingDetailsRead:
    """Create tracking details for a user.

    Args:
        user_id: ID of the user
        tracking: Tracking details to create
        db: Database connection

    Returns:
        Created tracking details record

    Raises:
        HTTPException: If user is not found
    """
    # Check if user exists
    user_query = """
        SELECT id FROM "user" WHERE id = :user_id
    """

    user_row = await db.fetch_one(user_query, {"user_id": user_id})

    if user_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    # Check if user already has tracking
    tracking_query = """
        SELECT id FROM tracking_details WHERE user_id = :user_id
    """

    tracking_row = await db.fetch_one(tracking_query, {"user_id": user_id})

    if tracking_row is not None:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT, detail="User already has tracking"
        )

    # Insert tracking details
    insert_query = """
        INSERT INTO tracking_details (authentication_code, initial_match_token, user_id)
        VALUES (:authentication_code, :initial_match_token, :user_id)
        RETURNING id, authentication_code, initial_match_token, user_id
    """

    values = {
        "authentication_code": tracking.authentication_code,
        "initial_match_token": tracking.initial_match_token,
        "user_id": user_id,
    }

    try:
        tracking_row = await db.fetch_one(insert_query, values)
        if tracking_row is None:
            _raise_tracking_creation_error()

        # Use Pydantic to validate and type the database result
        # Type checker: tracking_row is guaranteed to be non-None here due to the exception above
        assert tracking_row is not None
        return TrackingDetailsRead(
            id=tracking_row["id"],
            authentication_code=tracking_row["authentication_code"],
            initial_match_token=tracking_row["initial_match_token"],
            user_id=tracking_row["user_id"],
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create tracking details: {e!s}",
        ) from e


async def read_tracking_details(
    user_id: int,
    db: Annotated[databases.Database, Depends(get_async_connection_dependency)],
) -> TrackingDetailsRead:
    """Read tracking details for a user.

    Args:
        user_id: ID of the user
        db: Database connection

    Returns:
        Tracking details record

    Raises:
        HTTPException: If user is not found or has no tracking
    """
    # Check if user exists and get tracking details
    query = """
        SELECT u.id as user_id, t.id, t.authentication_code, t.initial_match_token
        FROM "user" u
        LEFT JOIN tracking_details t ON u.id = t.user_id
        WHERE u.id = :user_id
    """

    row = await db.fetch_one(query, {"user_id": user_id})

    if row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    if row["id"] is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User has no tracking details"
        )

    # Use Pydantic to validate and type the database result
    return TrackingDetailsRead(
        id=row["id"],
        authentication_code=row["authentication_code"],
        initial_match_token=row["initial_match_token"],
        user_id=row["user_id"],
    )


async def create_demo_file(
    demo: DemoFileCreate, db: databases.Database
) -> DemoFileRead:
    """Create a new demo file record.

    Args:
        demo: The demo file data to create
        db: Database connection

    Returns:
        Created demo file record

    Raises:
        HTTPException: If demo file creation fails
    """
    query = """
        INSERT INTO demo_file (match_id, file_path, status, created_at, updated_at)
        VALUES (:match_id, :file_path, :status, :created_at, :updated_at)
        RETURNING id, match_id, file_path, status, error_message, created_at, updated_at
    """

    values = {
        "match_id": demo.match_id,
        "file_path": demo.file_path,
        "status": demo.status.value,
        "created_at": datetime.now(UTC),
        "updated_at": datetime.now(UTC),
    }

    try:
        demo_row = await db.fetch_one(query, values)
        if demo_row is None:
            _raise_demo_creation_error()

        # Use Pydantic to validate and type the database result
        # Type checker: demo_row is guaranteed to be non-None here due to the exception above
        assert demo_row is not None
        return DemoFileRead(
            id=demo_row["id"],
            match_id=demo_row["match_id"],
            file_path=demo_row["file_path"],
            status=demo_row["status"],
            error_message=demo_row["error_message"],
            created_at=demo_row["created_at"],
            updated_at=demo_row["updated_at"],
            processing_jobs=[],
            match=None,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create demo file: {e!s}",
        ) from e


async def read_demo_file(demo_id: int, db: databases.Database) -> DemoFileRead:
    """Read a demo file by ID.

    Args:
        demo_id: ID of the demo file
        db: Database connection

    Returns:
        Demo file record

    Raises:
        HTTPException: If demo file is not found
    """
    query = """
        SELECT id, match_id, file_path, status, error_message, created_at, updated_at
        FROM demo_file
        WHERE id = :demo_id
    """

    demo_row = await db.fetch_one(query, {"demo_id": demo_id})

    if demo_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Demo file not found"
        )

    return DemoFileRead(
        id=demo_row["id"],
        match_id=demo_row["match_id"],
        file_path=demo_row["file_path"],
        status=demo_row["status"],
        error_message=demo_row["error_message"],
        created_at=demo_row["created_at"],
        updated_at=demo_row["updated_at"],
        processing_jobs=[],
        match=None,
    )


async def read_demo_file_by_match(
    match_id: str, db: databases.Database
) -> DemoFileRead:
    """Read a demo file by match ID.

    Args:
        match_id: Match ID of the demo file
        db: Database connection

    Returns:
        Demo file record

    Raises:
        HTTPException: If demo file is not found
    """
    query = """
        SELECT id, match_id, file_path, status, error_message, created_at, updated_at
        FROM demo_file
        WHERE match_id = :match_id
    """

    demo_row = await db.fetch_one(query, {"match_id": match_id})

    if demo_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Demo file not found"
        )

    return DemoFileRead(
        id=demo_row["id"],
        match_id=demo_row["match_id"],
        file_path=demo_row["file_path"],
        status=demo_row["status"],
        error_message=demo_row["error_message"],
        created_at=demo_row["created_at"],
        updated_at=demo_row["updated_at"],
        processing_jobs=[],
        match=None,
    )


async def list_demo_files(db: databases.Database) -> list[DemoFileRead]:
    """List all demo files.

    Args:
        db: Database connection

    Returns:
        List of demo file records
    """
    query = """
        SELECT id, match_id, file_path, status, error_message, created_at, updated_at
        FROM demo_file
        ORDER BY created_at DESC
    """

    rows = await db.fetch_all(query)

    return [
        DemoFileRead(
            id=row["id"],
            match_id=row["match_id"],
            file_path=row["file_path"],
            status=row["status"],
            error_message=row["error_message"],
            created_at=row["created_at"],
            updated_at=row["updated_at"],
            processing_jobs=[],
            match=None,
        )
        for row in rows
    ]


async def update_demo_file(
    demo_id: int, update_data: dict[str, str], db: databases.Database
) -> DemoFileRead:
    """Update a demo file record.

    Args:
        demo_id: ID of the demo file to update
        update_data: Dictionary of fields to update
        db: Database connection

    Returns:
        Updated demo file record

    Raises:
        HTTPException: If demo file is not found
    """
    # Allowlist of valid fields to prevent SQL injection
    allowed_fields = {"status", "error_message"}

    # Build safe update query using allowlist
    set_clauses = []
    params: dict[str, Any] = {"demo_id": demo_id, "updated_at": datetime.now(UTC)}

    for key, value in update_data.items():
        if key in allowed_fields:
            # Safe to use field name since it's from allowlist
            set_clauses.append(f"{key} = :{key}")
            params[key] = value

    if not set_clauses:
        # No valid fields to update, just return current record
        return await read_demo_file(demo_id, db)

    set_clauses.append("updated_at = :updated_at")

    # Build query with allowlisted field names (safe from SQL injection)
    set_clause_str = ", ".join(set_clauses)
    query = f"""
        UPDATE demo_file
        SET {set_clause_str}
        WHERE id = :demo_id
        RETURNING id, match_id, file_path, status, error_message, created_at, updated_at
    """  # noqa: S608

    try:
        demo_row = await db.fetch_one(query, params)

        if demo_row is None:
            _raise_demo_not_found_error()

        # Type checker: demo_row is guaranteed to be non-None here due to the exception above
        assert demo_row is not None
        return DemoFileRead(
            id=demo_row["id"],
            match_id=demo_row["match_id"],
            file_path=demo_row["file_path"],
            status=demo_row["status"],
            error_message=demo_row["error_message"],
            created_at=demo_row["created_at"],
            updated_at=demo_row["updated_at"],
            processing_jobs=[],
            match=None,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update demo file: {e!s}",
        ) from e


async def create_match(
    match_data: dict[str, Any], demo_file_id: int, db: databases.Database
) -> None:
    """Create a match record with players and rounds.

    Args:
        match_data: Dictionary containing match information
        demo_file_id: ID of the demo file this match was extracted from
        db: Database connection

    Raises:
        HTTPException: If match creation fails
    """
    # Insert match record
    map_name = match_data.get("map", {}).get("name", "unknown")

    match_query = """
        INSERT INTO "match" (map_name, demo_file_id, created_at)
        VALUES (:map_name, :demo_file_id, :created_at)
        RETURNING id
    """

    match_values = {
        "map_name": map_name,
        "demo_file_id": demo_file_id,
        "created_at": datetime.now(UTC),
    }

    try:
        match_row = await db.fetch_one(match_query, match_values)
        if match_row is None:
            _raise_match_creation_error()

        # Type checker: match_row is guaranteed to be non-None here due to the exception above
        assert match_row is not None
        match_id = match_row["id"]

        # Insert players
        players_data = match_data.get("players", [])
        for player in players_data:
            player_query = """
                INSERT INTO match_player (steam_id, name, team, match_id)
                VALUES (:steam_id, :name, :team, :match_id)
            """
            player_values = {
                "steam_id": player.get("steam_id", ""),
                "name": player.get("name", ""),
                "team": player.get("team", "UNKNOWN"),
                "match_id": match_id,
            }
            await db.execute(player_query, player_values)

        # Insert rounds
        rounds_data = match_data.get("rounds", [])
        for round_data in rounds_data:
            round_query = """
                INSERT INTO match_round (round_number, winner, win_reason, ct_score, t_score, match_id)
                VALUES (:round_number, :winner, :win_reason, :ct_score, :t_score, :match_id)
            """
            round_values = {
                "round_number": round_data.get("round_number", 0),
                "winner": round_data.get("winner", "UNKNOWN"),
                "win_reason": round_data.get("win_reason", "UNKNOWN"),
                "ct_score": round_data.get("ct_score", 0),
                "t_score": round_data.get("t_score", 0),
                "match_id": match_id,
            }
            await db.execute(round_query, round_values)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create match: {e!s}",
        ) from e


async def read_match(match_id: int, db: databases.Database) -> MatchRead:
    """Read a match by ID.

    Args:
        match_id: ID of the match
        db: Database connection

    Returns:
        Match record with players and rounds

    Raises:
        HTTPException: If match is not found
    """
    # Get match data
    match_query = """
        SELECT id, map_name, demo_file_id, created_at
        FROM "match"
        WHERE id = :match_id
    """

    match_row = await db.fetch_one(match_query, {"match_id": match_id})

    if match_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Match not found"
        )

    # Get players
    players_query = """
        SELECT id, steam_id, name, team, match_id
        FROM match_player
        WHERE match_id = :match_id
    """

    players_rows = await db.fetch_all(players_query, {"match_id": match_id})

    players = [
        MatchPlayerRead(
            id=player_row["id"],
            steam_id=player_row["steam_id"],
            name=player_row["name"],
            team=player_row["team"],
            match_id=player_row["match_id"],
        )
        for player_row in players_rows
    ]

    # Get rounds
    rounds_query = """
        SELECT id, round_number, winner, win_reason, ct_score, t_score, match_id
        FROM match_round
        WHERE match_id = :match_id
        ORDER BY round_number
    """

    rounds_rows = await db.fetch_all(rounds_query, {"match_id": match_id})

    rounds = [
        MatchRoundRead(
            id=round_row["id"],
            round_number=round_row["round_number"],
            winner=round_row["winner"],
            win_reason=round_row["win_reason"],
            ct_score=round_row["ct_score"],
            t_score=round_row["t_score"],
            match_id=round_row["match_id"],
        )
        for round_row in rounds_rows
    ]

    return MatchRead(
        id=match_row["id"],
        map_name=match_row["map_name"],
        demo_file_id=match_row["demo_file_id"],
        created_at=match_row["created_at"],
        players=players,
        rounds=rounds,
    )


async def read_match_by_demo_file(
    demo_file_id: int, db: databases.Database
) -> MatchRead:
    """Read a match by demo file ID.

    Args:
        demo_file_id: ID of the demo file
        db: Database connection

    Returns:
        Match record with players and rounds

    Raises:
        HTTPException: If match is not found
    """
    # Get match data
    match_query = """
        SELECT id, map_name, demo_file_id, created_at
        FROM "match"
        WHERE demo_file_id = :demo_file_id
    """

    match_row = await db.fetch_one(match_query, {"demo_file_id": demo_file_id})

    if match_row is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Match not found for demo file",
        )

    # Use the existing read_match function to get full match data
    return await read_match(match_row["id"], db)


async def list_matches(db: databases.Database) -> list[MatchRead]:
    """List all matches.

    Args:
        db: Database connection

    Returns:
        List of match records with players and rounds
    """
    # Optimized query to get all matches with their data in one go
    matches_query = """
        SELECT id, map_name, demo_file_id, created_at
        FROM "match"
        ORDER BY created_at DESC
    """

    match_rows = await db.fetch_all(matches_query)

    if not match_rows:
        return []

    match_ids = [row["id"] for row in match_rows]

    # Batch load all players for all matches
    # Create IN clause for SQLite compatibility
    match_ids_placeholder = ", ".join(
        [":match_id_" + str(i) for i in range(len(match_ids))]
    )
    match_ids_params = {
        f"match_id_{i}": match_id for i, match_id in enumerate(match_ids)
    }

    players_query = f"""
        SELECT id, match_id, steam_id, name, team
        FROM match_player
        WHERE match_id IN ({match_ids_placeholder})
        ORDER BY match_id, id
    """  # noqa: S608

    players_rows = await db.fetch_all(players_query, match_ids_params)

    # Batch load all rounds for all matches
    rounds_query = f"""
        SELECT id, match_id, round_number, winner, win_reason, ct_score, t_score
        FROM match_round
        WHERE match_id IN ({match_ids_placeholder})
        ORDER BY match_id, round_number
    """  # noqa: S608

    rounds_rows = await db.fetch_all(rounds_query, match_ids_params)

    # Group players and rounds by match_id
    players_by_match = {}
    for player_row in players_rows:
        match_id = player_row["match_id"]
        if match_id not in players_by_match:
            players_by_match[match_id] = []
        players_by_match[match_id].append(
            MatchPlayerRead(
                id=player_row["id"],
                steam_id=player_row["steam_id"],
                name=player_row["name"],
                team=player_row["team"],
                match_id=player_row["match_id"],
            )
        )

    rounds_by_match = {}
    for round_row in rounds_rows:
        match_id = round_row["match_id"]
        if match_id not in rounds_by_match:
            rounds_by_match[match_id] = []
        rounds_by_match[match_id].append(
            MatchRoundRead(
                id=round_row["id"],
                round_number=round_row["round_number"],
                winner=round_row["winner"],
                win_reason=round_row["win_reason"],
                ct_score=round_row["ct_score"],
                t_score=round_row["t_score"],
                match_id=round_row["match_id"],
            )
        )

    # Build match objects
    matches = []
    for match_row in match_rows:
        match_id = match_row["id"]
        matches.append(
            MatchRead(
                id=match_id,
                map_name=match_row["map_name"],
                demo_file_id=match_row["demo_file_id"],
                created_at=match_row["created_at"],
                players=players_by_match.get(match_id, []),
                rounds=rounds_by_match.get(match_id, []),
            )
        )

    return matches
