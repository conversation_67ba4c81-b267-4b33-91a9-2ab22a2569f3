"""FastAPI dependencies for services and clients."""

from functools import lru_cache
from typing import Annotated

from fastapi import Depends

from backend import demo_storage
from backend.clients.demo_processing_client import DemoProcessingClient
from backend.clients.steam_api import SteamAPI, get_steam_api
from backend.config import Settings, get_settings
from backend.services.demo_service import DemoService
from backend.services.match_service import MatchService
from backend.services.user_service import UserService


def get_settings_dependency() -> Settings:
    """Get application settings (cached).

    Returns:
        Settings instance
    """
    return get_settings()


def get_demo_processing_client(
    settings: Annotated[Settings, Depends(get_settings_dependency)],
) -> DemoProcessingClient:
    """Get demo processing client instance (thread-safe, per-request).

    Creates a new client instance for each request to avoid thread safety issues.
    The client will be properly initialized when methods are called.

    Args:
        settings: Application settings

    Returns:
        DemoProcessingClient instance
    """
    # Client will be initialized lazily when methods are called
    return DemoProcessingClient(broker_url=str(settings.redis_url))


async def cleanup_demo_processing_client():
    """Cleanup function for backward compatibility.

    This is now a no-op since we use per-request clients.
    """


@lru_cache
def get_demo_storage() -> demo_storage.DemoStorage:
    """Get demo storage instance (cached).

    Returns:
        DemoStorage instance
    """
    return demo_storage.get_demo_storage()


def get_user_service(
    steam_api: Annotated[SteamAPI, Depends(get_steam_api)],
) -> UserService:
    """Get user service instance.

    Args:
        steam_api: Steam API client

    Returns:
        UserService instance
    """
    return UserService(steam_api)


def get_match_service(
    steam_api: Annotated[SteamAPI, Depends(get_steam_api)],
) -> MatchService:
    """Get match service instance.

    Args:
        steam_api: Steam API client

    Returns:
        MatchService instance
    """
    return MatchService(steam_api=steam_api)


def get_demo_service(
    demo_processing_client: Annotated[
        DemoProcessingClient, Depends(get_demo_processing_client)
    ],
    steam_api: Annotated[SteamAPI, Depends(get_steam_api)],
    demo_storage_instance: Annotated[
        demo_storage.DemoStorage, Depends(get_demo_storage)
    ],
) -> DemoService:
    """Get demo service instance.

    Args:
        demo_processing_client: Demo processing microservice client
        steam_api: Steam API client
        demo_storage_instance: Demo storage handler

    Returns:
        DemoService instance
    """
    return DemoService(
        demo_processing_client=demo_processing_client,
        steam_api=steam_api,
        demo_storage=demo_storage_instance,
    )
