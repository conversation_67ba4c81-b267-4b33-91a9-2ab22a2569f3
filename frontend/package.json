{"name": "frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:unit": "vitest", "schema:generate": "openapi-typescript http://localhost:8000/openapi.json -o ./src/lib/api/schema.d.ts"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@playwright/test": "^1.52.0", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "@testing-library/user-event": "^14.6.1", "@types/better-sqlite3": "^7.6.12", "@types/jsonwebtoken": "^9.0.9", "autoprefixer": "^10.4.20", "bits-ui": "^1.4.8", "clsx": "^2.1.1", "drizzle-kit": "^0.30.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^2.46.1", "formsnap": "^2.0.0", "globals": "^15.14.0", "jsdom": "^26.1.0", "lucide-svelte": "^0.479.0", "openapi-typescript": "^7.6.1", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.10", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "sveltekit-superforms": "^2.24.0", "tailwind-merge": "^3.0.2", "tailwind-variants": "^0.2.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.0.0", "vitest": "^3.0.0", "zod": "^3.24.2"}, "dependencies": {"@openauthjs/openauth": "^0.4.3", "better-sqlite3": "^11.8.0", "drizzle-orm": "^0.38.4", "jsonwebtoken": "^9.0.2", "mode-watcher": "^0.5.1", "openapi-fetch": "^0.13.4", "valibot": "^1.1.0"}}