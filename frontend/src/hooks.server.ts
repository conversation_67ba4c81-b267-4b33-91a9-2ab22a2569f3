import { redirect, type <PERSON>le } from '@sveltejs/kit';
import { authClient } from '$lib/auth/client';
import { subjects } from '$lib/auth/subjects';

const authHandle: Handle = async ({ event, resolve }) => {
	// Initialize user as null
	event.locals.user = null;

	// Try to get auth tokens from cookies
	const authCookie = event.cookies.get('auth');

	if (authCookie) {
		try {
			const tokens = JSON.parse(authCookie);

			if (tokens.access) {
				// Verify the token
				const verified = await authClient.verify(subjects, tokens.access, {
					refresh: tokens.refresh
				});

				if (!verified.err) {
					event.locals.user = verified.subject.properties;

					// Update tokens if refreshed
					if (verified.tokens) {
						event.cookies.set(
							'auth',
							JSON.stringify({
								access: verified.tokens.access,
								refresh: verified.tokens.refresh
							}),
							{
								sameSite: 'strict',
								path: '/',
								maxAge: 7 * 24 * 60 * 60 // 7 days
							}
						);
					}
				} else {
					// Clear invalid tokens
					event.cookies.delete('auth', { path: '/' });
				}
			}
		} catch (error) {
			console.error('Auth token parsing error:', error);
			event.cookies.delete('auth', { path: '/' });
		}
	}

	return resolve(event);
};

const authorizationHandle: Handle = async ({ event, resolve }) => {
	const routeId = event.route.id;

	if (routeId && routeId.startsWith('/(app)')) {
		if (!event.locals.user) {
			// Redirect to the signin page
			throw redirect(303, '/login');
		}
	}

	return resolve(event);
};

export const handle: Handle = async ({ event, resolve }) => {
	// Run auth handle first
	await authHandle({ event, resolve: async () => new Response() });

	// Then run authorization handle
	return authorizationHandle({ event, resolve });
};
