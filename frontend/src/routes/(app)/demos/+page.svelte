<script lang="ts">
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import * as Card from '$lib/components/ui/card';
	import * as Table from '$lib/components/ui/table';
	// import { Progress } from '$lib/components/ui/progress';
	import {
		Upload,
		Loader2,
		FileText,
		CheckCircle,
		XCircle,
		Clock,
		AlertCircle,
		// RefreshCw,
		// Trash2,
		// Download,
		Eye
	} from 'lucide-svelte';
	import { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';
	import { z } from 'zod';

	// Client-side schema - use z.any() to avoid FileList SSR issues
	export const uploadSchema = z.object({
		matchId: z.string().min(1, { message: 'Match ID is required.' }),
		demoFile: z.any().optional() // Use z.any() to avoid FileList reference in SSR
	});

	interface Demo {
		id: number;
		match_id: string;
		status: string;
		created_at: string;
		file_path: string;
		error_message: string | null;
	}

	interface PageData {
		demos: Demo[];
		form: SuperValidated<Infer<typeof uploadSchema>>;
	}

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	const form = superForm(data.form, {
		validators: zod(uploadSchema),
		resetForm: true,
		onResult: ({ result }) => {
			if (result.type === 'success' && result.data?.success) {
				uploadSuccess = result.data.message || 'Upload successful!';
				isDragOver = false;
				// Handle redirect if provided
				if (result.data.redirect) {
					setTimeout(() => {
						window.location.href = result.data?.redirect || '/';
					}, 1500);
				}
			} else if (
				result.type === 'failure' ||
				(result.type === 'success' && !result.data?.success)
			) {
				uploadError = result.data?.message || 'Upload failed.';
			} else if (result.type === 'error') {
				uploadError = result.error.message || 'An unexpected error occurred.';
			}
		},
		onUpdate: () => {
			uploadError = '';
			uploadSuccess = '';
		}
	});

	const { form: formData, enhance: formEnhance, submitting } = form;

	let uploading = $derived($submitting);
	let uploadError = $state('');
	let uploadSuccess = $state('');
	let isDragOver = $state(false);

	function getStatusInfo(status: string) {
		switch (status) {
			case 'PROCESSED':
				return {
					icon: CheckCircle,
					color: 'text-green-500',
					bgColor: 'bg-green-500/10',
					variant: 'default' as const,
					label: 'Processed'
				};
			case 'PROCESSING':
				return {
					icon: Clock,
					color: 'text-yellow-500',
					bgColor: 'bg-yellow-500/10',
					variant: 'secondary' as const,
					label: 'Processing'
				};
			case 'FAILED':
				return {
					icon: XCircle,
					color: 'text-red-500',
					bgColor: 'bg-red-500/10',
					variant: 'destructive' as const,
					label: 'Failed'
				};
			case 'PENDING':
				return {
					icon: Clock,
					color: 'text-gray-500',
					bgColor: 'bg-gray-500/10',
					variant: 'outline' as const,
					label: 'Pending'
				};
			default:
				return {
					icon: AlertCircle,
					color: 'text-gray-500',
					bgColor: 'bg-gray-500/10',
					variant: 'outline' as const,
					label: 'Unknown'
				};
		}
	}

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('en-US', {
			month: 'short',
			day: 'numeric',
			year: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// function formatFileSize(bytes: number) {
	// 	if (bytes === 0) return '0 Bytes';
	// 	const k = 1024;
	// 	const sizes = ['Bytes', 'KB', 'MB', 'GB'];
	// 	const i = Math.floor(Math.log(bytes) / Math.log(k));
	// 	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	// }

	function handleDragOver(event: DragEvent) {
		event.preventDefault();
		isDragOver = true;
	}

	function handleDragLeave(event: DragEvent) {
		event.preventDefault();
		isDragOver = false;
	}

	function handleDrop(event: DragEvent) {
		event.preventDefault();
		isDragOver = false;

		const files = event.dataTransfer?.files;
		if (files && files.length > 0) {
			const file = files[0];
			if (file && file.name.endsWith('.dem')) {
				const dt = new DataTransfer();
				dt.items.add(file);
				$formData.demoFile = dt.files;
			} else {
				uploadError = 'Please select a valid .dem file';
			}
		}
	}

	// Calculate statistics
	let stats = $derived({
		total: data.demos.length,
		processed: data.demos.filter((d) => d.status === 'PROCESSED').length,
		processing: data.demos.filter((d) => d.status === 'PROCESSING').length,
		failed: data.demos.filter((d) => d.status === 'FAILED').length,
		pending: data.demos.filter((d) => d.status === 'PENDING').length,
		successRate:
			data.demos.length > 0
				? Math.round(
						(data.demos.filter((d) => d.status === 'PROCESSED').length / data.demos.length) * 100
					)
				: 0
	});
</script>

<svelte:head>
	<title>Demo Management - Brainless Stats</title>
</svelte:head>

<div class="container mx-auto space-y-6 p-6">
	<!-- Header -->
	<div class="flex flex-col gap-2">
		<h1 class="text-3xl font-bold tracking-tight">Demo Management</h1>
		<p class="text-muted-foreground">Upload and manage your CS2 demo files for analysis</p>
	</div>

	<!-- Statistics Cards -->
	{#if data.demos.length > 0}
		<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
			<Card.Root>
				<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
					<Card.Title class="text-sm font-medium">Total Demos</Card.Title>
					<FileText class="h-4 w-4 text-muted-foreground" />
				</Card.Header>
				<Card.Content>
					<div class="text-2xl font-bold">{stats.total}</div>
				</Card.Content>
			</Card.Root>

			<Card.Root>
				<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
					<Card.Title class="text-sm font-medium">Processed</Card.Title>
					<CheckCircle class="h-4 w-4 text-green-500" />
				</Card.Header>
				<Card.Content>
					<div class="text-2xl font-bold">{stats.processed}</div>
				</Card.Content>
			</Card.Root>

			<Card.Root>
				<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
					<Card.Title class="text-sm font-medium">Processing</Card.Title>
					<Clock class="h-4 w-4 text-yellow-500" />
				</Card.Header>
				<Card.Content>
					<div class="text-2xl font-bold">{stats.processing}</div>
				</Card.Content>
			</Card.Root>

			<Card.Root>
				<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
					<Card.Title class="text-sm font-medium">Failed</Card.Title>
					<XCircle class="h-4 w-4 text-red-500" />
				</Card.Header>
				<Card.Content>
					<div class="text-2xl font-bold">{stats.failed}</div>
				</Card.Content>
			</Card.Root>

			<Card.Root>
				<Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
					<Card.Title class="text-sm font-medium">Success Rate</Card.Title>
					<Upload class="h-4 w-4 text-muted-foreground" />
				</Card.Header>
				<Card.Content>
					<div class="text-2xl font-bold">{stats.successRate}%</div>
				</Card.Content>
			</Card.Root>
		</div>
	{/if}

	<div class="grid gap-6 lg:grid-cols-2">
		<!-- Upload Form -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Upload class="h-5 w-5" />
					Upload Demo File
				</Card.Title>
				<Card.Description>
					Upload a CS2 demo file (.dem) with a match ID for analysis
				</Card.Description>
			</Card.Header>
			<Card.Content>
				<form
					method="POST"
					action="?/upload"
					enctype="multipart/form-data"
					use:formEnhance
					onsubmit={() => {
						uploadError = '';
						uploadSuccess = '';
					}}
				>
					<div class="grid gap-4">
						<Form.Field {form} name="matchId">
							<Form.Control>
								{#snippet children(attrs)}
									<Form.Label>Match ID</Form.Label>
									<Input
										{...attrs}
										name="matchId"
										placeholder="Enter match ID (e.g., MATCH_2024_001)"
										bind:value={$formData.matchId}
										required
									/>
								{/snippet}
							</Form.Control>
							<Form.Description>
								A unique identifier for this match - use any descriptive name
							</Form.Description>
						</Form.Field>

						<Form.Field {form} name="demoFile">
							<Form.Control>
								{#snippet children(attrs)}
									<Form.Label>Demo File</Form.Label>
									<!-- Drag & Drop Area -->
									<div
										role="button"
										tabindex="0"
										aria-label="Drag and drop demo file or click to browse"
										class="relative rounded-lg border-2 border-dashed p-6 transition-all {isDragOver
											? 'border-primary bg-primary/5'
											: 'border-muted-foreground/25 hover:border-muted-foreground/50'}"
										ondragover={handleDragOver}
										ondragleave={handleDragLeave}
										ondrop={handleDrop}
										onclick={() => {
											const fileInput = document.getElementById('demo-file') as HTMLInputElement;
											fileInput?.click();
										}}
										onkeydown={(event) => {
											if (event.key === 'Enter' || event.key === ' ') {
												event.preventDefault();
												const fileInput = document.getElementById('demo-file') as HTMLInputElement;
												fileInput?.click();
											}
										}}
									>
										<div class="flex flex-col items-center justify-center space-y-3 text-center">
											<div class="flex h-12 w-12 items-center justify-center rounded-full bg-muted">
												<Upload class="h-6 w-6 text-muted-foreground" />
											</div>
											<div>
												<p class="text-sm font-medium">
													{#if $formData.demoFile && $formData.demoFile.length > 0}
														Selected: {$formData.demoFile[0]?.name || 'Unknown file'}
													{:else}
														Drag & drop your demo file here
													{/if}
												</p>
												<p class="text-xs text-muted-foreground">or click to browse</p>
											</div>
										</div>
									</div>
									<!-- Hidden file input -->
									<Input
										{...attrs}
										type="file"
										name="demoFile"
										id="demo-file"
										accept=".dem"
										bind:files={$formData.demoFile}
										required
										class="sr-only"
									/>
								{/snippet}
							</Form.Control>
							<Form.Description>Select a .dem file to upload (max 100MB)</Form.Description>
						</Form.Field>

						{#if uploadError && !$submitting}
							<div
								class="flex items-center gap-2 rounded-md bg-destructive/15 p-3 text-sm text-destructive"
							>
								<AlertCircle class="h-4 w-4 flex-shrink-0" />
								{uploadError}
							</div>
						{/if}

						{#if uploadSuccess && !$submitting}
							<div
								class="flex items-center gap-2 rounded-md bg-green-500/15 p-3 text-sm text-green-500"
							>
								<CheckCircle class="h-4 w-4 flex-shrink-0" />
								{uploadSuccess}
							</div>
						{/if}

						<Button type="submit" disabled={uploading} class="w-full">
							{#if uploading}
								<Loader2 class="mr-2 h-4 w-4 animate-spin" />
								<span>Uploading...</span>
							{:else}
								<Upload class="mr-2 h-4 w-4" />
								<span>Upload Demo</span>
							{/if}
						</Button>
					</div>
				</form>
			</Card.Content>
		</Card.Root>

		<!-- Recent Activity -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<FileText class="h-5 w-5" />
					Recent Activity
				</Card.Title>
				<Card.Description>Latest demo uploads and processing status</Card.Description>
			</Card.Header>
			<Card.Content>
				{#if data.demos.length > 0}
					<div class="space-y-3">
						{#each data.demos.slice(0, 5) as demo}
							{@const statusInfo = getStatusInfo(demo.status)}
							{@const IconComponent = statusInfo.icon}
							<div
								class="flex items-center gap-3 rounded-lg border p-3 transition-colors hover:bg-muted/50"
							>
								<div
									class="h-10 w-10 rounded-full {statusInfo.bgColor} flex items-center justify-center"
								>
									<IconComponent class="h-4 w-4 {statusInfo.color}" />
								</div>
								<div class="flex-1 space-y-1">
									<div class="flex items-center gap-2">
										<span class="text-sm font-medium">{demo.match_id}</span>
										<Badge variant={statusInfo.variant} class="text-xs">
											{statusInfo.label}
										</Badge>
									</div>
									<div class="text-xs text-muted-foreground">
										{formatDate(demo.created_at)}
									</div>
									{#if demo.error_message}
										<div class="truncate text-xs text-red-500">
											{demo.error_message}
										</div>
									{/if}
								</div>
								{#if demo.status === 'PROCESSED'}
									<Button size="sm" variant="ghost" href="/matches">
										<Eye class="h-3 w-3" />
									</Button>
								{/if}
							</div>
						{/each}
					</div>
				{:else}
					<div class="flex flex-col items-center justify-center py-8 text-center">
						<FileText class="mb-4 h-12 w-12 text-muted-foreground/30" />
						<h3 class="mb-2 font-medium">No demos uploaded yet</h3>
						<p class="text-sm text-muted-foreground">Upload your first demo file to get started</p>
					</div>
				{/if}
			</Card.Content>
		</Card.Root>
	</div>

	<!-- Demo Files List -->
	{#if data.demos.length > 0}
		<Card.Root>
			<Card.Header>
				<div class="flex items-center justify-between">
					<div>
						<Card.Title class="flex items-center gap-2">
							<FileText class="h-5 w-5" />
							All Demo Files
						</Card.Title>
						<Card.Description>Complete list of your uploaded demo files</Card.Description>
					</div>
					<Badge variant="secondary">{data.demos.length} total</Badge>
				</div>
			</Card.Header>
			<Card.Content>
				<div class="rounded-md border">
					<Table.Root>
						<Table.Header>
							<Table.Row>
								<Table.Head class="w-[200px]">Match ID</Table.Head>
								<Table.Head class="w-[120px]">Status</Table.Head>
								<Table.Head class="w-[150px]">Uploaded</Table.Head>
								<Table.Head>Error Message</Table.Head>
								<Table.Head class="w-[100px]">Actions</Table.Head>
							</Table.Row>
						</Table.Header>
						<Table.Body>
							{#each data.demos as demo}
								{@const statusInfo = getStatusInfo(demo.status)}
								{@const IconComponent = statusInfo.icon}
								<Table.Row class="hover:bg-muted/50">
									<Table.Cell>
										<div class="font-medium">{demo.match_id}</div>
									</Table.Cell>
									<Table.Cell>
										<div class="flex items-center gap-2">
											<IconComponent class="h-3 w-3 {statusInfo.color}" />
											<Badge variant={statusInfo.variant} class="text-xs">
												{statusInfo.label}
											</Badge>
										</div>
									</Table.Cell>
									<Table.Cell>
										<span class="text-sm text-muted-foreground">
											{formatDate(demo.created_at)}
										</span>
									</Table.Cell>
									<Table.Cell>
										{#if demo.error_message}
											<div
												class="max-w-xs truncate text-sm text-red-500"
												title={demo.error_message}
											>
												{demo.error_message}
											</div>
										{:else}
											<span class="text-sm text-muted-foreground">—</span>
										{/if}
									</Table.Cell>
									<Table.Cell>
										<div class="flex gap-1">
											{#if demo.status === 'PROCESSED'}
												<Button size="sm" variant="ghost" href="/matches">
													<Eye class="h-3 w-3" />
												</Button>
											{/if}
										</div>
									</Table.Cell>
								</Table.Row>
							{/each}
						</Table.Body>
					</Table.Root>
				</div>
			</Card.Content>
		</Card.Root>
	{/if}
</div>
