import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ locals, route }) => {
	if (!locals.user) {
		redirect(302, '/login');
	}

	// Redirect to settings if tracking is not configured
	if (!locals.user.hasTracking && route.id !== '/(app)/settings') {
		redirect(302, '/settings');
	}

	return {
		user: locals.user
	};
};
