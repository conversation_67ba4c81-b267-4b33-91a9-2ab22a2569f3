<script lang="ts">
	import { authService } from '$lib/auth/client';
	import { page } from '$app/stores';

	let loading = false;
	let error = '';

	// Check for error from URL params
	$: if ($page.url.searchParams.has('error')) {
		const errorType = $page.url.searchParams.get('error');
		error = getErrorMessage(errorType);
	}

	async function handleSteamLogin() {
		loading = true;
		error = '';

		try {
			await authService.initiateLogin();
		} catch {
			error = 'Failed to initiate login. Please try again.';
			loading = false;
		}
	}

	function getErrorMessage(errorType: string | null): string {
		switch (errorType) {
			case 'auth_failed':
				return 'Steam authentication failed. Please try again.';
			case 'missing_code':
				return 'Authentication code missing. Please try again.';
			case 'missing_state':
				return 'Authentication state missing. Please try again.';
			case 'callback_failed':
				return 'Authentication processing failed. Please try again.';
			default:
				return 'An error occurred during login.';
		}
	}
</script>

<div class="font-alterra flex h-screen w-screen items-center justify-center">
	<div class="items-center justify-center rounded-none border-2 border-foreground p-8 uppercase">
		<h1 class="mb-6 text-2xl font-bold uppercase tracking-wider">Login interface</h1>

		{#if error}
			<div class="mb-4 border border-red-500 bg-red-50 p-3 text-red-700">
				{error}
			</div>
		{/if}

		<div class="mb-6">
			<div class="mb-3 h-1 w-full bg-foreground"></div>
			<p class="mb-2 text-sm leading-none">authentication required</p>
			<div class="h-1 w-full bg-foreground"></div>
		</div>

		<p class="mb-4 text-sm">select login method:</p>

		<button
			type="button"
			class="flex w-full items-center justify-center border border-foreground p-1 transition-colors hover:bg-foreground hover:text-background disabled:opacity-50"
			on:click={handleSteamLogin}
			disabled={loading}
		>
			<span class="mr-4">&gt;</span>
			<span class="pt-1 text-sm font-bold leading-none tracking-wider">
				{loading ? 'Connecting...' : 'Steam'}
			</span>
		</button>
	</div>
</div>
