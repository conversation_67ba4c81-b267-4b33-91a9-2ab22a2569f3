import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ url }) => {
	const code = url.searchParams.get('code');
	const error = url.searchParams.get('error');
	const state = url.searchParams.get('state');

	if (error) {
		console.error('Auth error:', error);
		throw redirect(302, '/login?error=auth_failed');
	}

	if (!code) {
		throw redirect(302, '/login?error=missing_code');
	}

	if (!state) {
		throw redirect(302, '/login?error=missing_state');
	}

	// Pass the code to the client-side for handling
	// We can't handle the full callback server-side because we need browser APIs
	return {
		code,
		state
	};
};
