<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { authService } from '$lib/auth/client';
	import type { PageData } from './$types';

	export let data: PageData;

	let loading = true;
	let error = '';

	onMount(async () => {
		try {
			const user = await authService.handleCallback(data.code);

			// Redirect based on user state
			if (!user.hasTracking) {
				await goto('/settings?setup=tracking');
			} else {
				await goto('/dashboard');
			}
		} catch (err) {
			console.error('Auth callback error:', err);
			error = err instanceof Error ? err.message : 'Authentication failed';
			loading = false;

			// Redirect to login with error after a delay
			setTimeout(() => {
				goto('/login?error=callback_failed');
			}, 3000);
		}
	});
</script>

<div class="font-alterra flex h-screen w-screen items-center justify-center">
	<div class="items-center justify-center rounded-none border-2 border-foreground p-8 uppercase">
		<h1 class="mb-6 text-2xl font-bold uppercase tracking-wider">Authentication</h1>

		{#if loading}
			<div class="mb-6">
				<div class="mb-3 h-1 w-full bg-foreground"></div>
				<p class="mb-2 text-sm leading-none">processing authentication...</p>
				<div class="h-1 w-full bg-foreground"></div>
			</div>

			<div class="flex items-center justify-center">
				<div class="h-8 w-8 animate-spin border-2 border-foreground border-t-transparent"></div>
			</div>
		{:else if error}
			<div class="mb-6">
				<div class="mb-3 h-1 w-full bg-red-500"></div>
				<p class="mb-2 text-sm leading-none text-red-700">authentication failed</p>
				<div class="h-1 w-full bg-red-500"></div>
			</div>

			<p class="mb-4 text-sm text-red-700">{error}</p>
			<p class="text-xs text-gray-500">redirecting to login...</p>
		{/if}
	</div>
</div>
